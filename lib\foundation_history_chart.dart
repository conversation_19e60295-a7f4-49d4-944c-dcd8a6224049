import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:developer' as developer;
import 'habit_analytics_service.dart';

/// Foundation History Chart - Built using Score Chart's Proven Architecture
/// 
/// This implementation precisely mirrors the FoundationScoreChart architecture:
/// 1. Perfect Y-axis and chart data alignment
/// 2. Proper spacing for all labels (top and bottom)
/// 3. Clean coordinate system
/// 4. Interactive value labels
/// 5. Horizontal scrolling with current period positioning
/// 6. The ONLY difference: displays bars instead of lines
class FoundationHistoryChart extends StatefulWidget {
  final HabitAnalyticsService analyticsService;
  final TimeScale timeScale;
  final ScrollController? controller;
  final double height;

  const FoundationHistoryChart({
    super.key,
    required this.analyticsService,
    required this.timeScale,
    this.controller,
    this.height = 240,
  });

  @override
  State<FoundationHistoryChart> createState() => _FoundationHistoryChartState();
}

class _FoundationHistoryChartState extends State<FoundationHistoryChart> {
  late ScrollController _scrollController;
  List<ChartDataPoint> _allDataPoints = [];
  bool _isLoading = true;
  
  // Layout constants - COPIED FROM SCORE CHART
  static const double _yAxisWidth = 50.0;
  static const double _topPadding = 60.0;    // Space for max value labels
  static const double _bottomPadding = 50.0; // Space for X-axis labels
  static const int _visibleDataPoints = 9; // FIXED: Show 9 visible points as requested
  
  // Dynamic layout variables - COPIED FROM SCORE CHART
  double _columnWidth = 80.0; // Same spacing as Score Chart
  double _chartAreaHeight = 0.0;
  double _baselineY = 0.0;
  
  @override
  void initState() {
    super.initState();
    developer.log('FoundationHistoryChart: initState called - timeScale: ${widget.timeScale.name}');
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void didUpdateWidget(FoundationHistoryChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.timeScale != widget.timeScale) {
      developer.log('FoundationHistoryChart: Time scale changed from ${oldWidget.timeScale.name} to ${widget.timeScale.name}');
      _loadInitialData();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // COPIED FROM SCORE CHART: Try to scroll when dependencies change (layout complete)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scheduleScrollToRecentData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  /// Load initial data - COPIED FROM SCORE CHART PATTERN
  Future<void> _loadInitialData() async {
    setState(() => _isLoading = true);
    
    try {
      developer.log('FoundationHistoryChart: Loading data for ${widget.timeScale.name}');
      
      // Use History data instead of Score data - THE ONLY DIFFERENCE
      final dataPoints = await widget.analyticsService.getHistoryDataForChart(widget.timeScale);
      
      setState(() {
        _allDataPoints = dataPoints;
        _isLoading = false;
      });
      
      developer.log('FoundationHistoryChart: Loaded ${dataPoints.length} data points');
      
      // DEBUG: Log first few data points to verify data
      for (int i = 0; i < dataPoints.length && i < 5; i++) {
        developer.log('FoundationHistoryChart: DataPoint $i - ${dataPoints[i].date}: ${dataPoints[i].value} (${dataPoints[i].label})');
      }
      
      // COPIED FROM SCORE CHART: Schedule scroll to recent data
      _scheduleScrollToRecentData();
      
    } catch (e) {
      developer.log('FoundationHistoryChart: Error loading data: $e');
      setState(() => _isLoading = false);
    }
  }

  /// FIXED: Schedule scroll to recent data with proper timing
  void _scheduleScrollToRecentData() {
    // FIXED: Use multiple delayed callbacks to ensure proper scrolling
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_scrollController.hasClients && _allDataPoints.isNotEmpty) {
          final maxScrollExtent = _scrollController.position.maxScrollExtent;
          developer.log('FoundationHistoryChart: Scrolling to recent data - maxScrollExtent: $maxScrollExtent');
          if (maxScrollExtent > 0) {
            _scrollController.animateTo(
              maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOutCubic,
            );
          }
        }
      });
    });
  }

  /// COPIED FROM SCORE CHART: Handle scroll events
  void _onScroll() {
    // Could implement infinite scroll here if needed
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    developer.log('FoundationHistoryChart: Building widget - isLoading: $_isLoading, dataPoints: ${_allDataPoints.length}');
    developer.log('FoundationHistoryChart: FORCE REBUILD - TimeScale: ${widget.timeScale.name}');
    
    if (_isLoading) {
      developer.log('FoundationHistoryChart: Showing loading indicator');
      return SizedBox(
        height: widget.height,
        child: const Center(child: CircularProgressIndicator()),
      );
    }
    
    if (_allDataPoints.isEmpty) {
      developer.log('FoundationHistoryChart: No data available, showing empty state');
      return SizedBox(
        height: widget.height,
        child: Center(
          child: Text(
            'No data available',
            style: GoogleFonts.inter(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        _calculateLayout(constraints.maxWidth);
        
        return SizedBox(
          height: widget.height,
          child: Stack(
            children: [
              // COPIED FROM SCORE CHART: Y-axis painter
              Positioned(
                left: 0,
                top: 0,
                bottom: 0,
                width: _yAxisWidth,
                child: CustomPaint(
                  painter: _YAxisPainter(
                    theme: theme,
                    chartAreaHeight: _chartAreaHeight,
                    baselineY: _baselineY,
                    topPadding: _topPadding,
                    maxValue: _getMaxValue(), // Different calculation for History
                  ),
                ),
              ),
              
              // COPIED FROM SCORE CHART: Main scrollable chart area
              Positioned(
                left: _yAxisWidth,
                top: 0,
                right: 0,
                bottom: 0,
                child: SingleChildScrollView(
                  controller: _scrollController,
                  scrollDirection: Axis.horizontal,
                  child: SizedBox(
                    width: _allDataPoints.length * _columnWidth,
                    child: CustomPaint(
                      painter: _HistoryBarPainter( // NEW: Bar painter instead of line painter
                        theme: theme,
                        dataPoints: _allDataPoints,
                        columnWidth: _columnWidth,
                        chartAreaHeight: _chartAreaHeight,
                        baselineY: _baselineY,
                        topPadding: _topPadding,
                        maxValue: _getMaxValue(),
                        timeScale: widget.timeScale,
                        analyticsService: widget.analyticsService, // Pass analytics service
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// FIXED: Calculate layout dimensions with proper infinite scroll support
  void _calculateLayout(double availableWidth) {
    final chartAreaWidth = availableWidth - _yAxisWidth;
    _chartAreaHeight = widget.height - _topPadding - _bottomPadding;
    _baselineY = widget.height - _bottomPadding;
    
    // FIXED: Always use fixed column width for infinite scroll
    // This ensures bars don't disappear when layout recalculates
    _columnWidth = chartAreaWidth / _visibleDataPoints; // Always show 9 points in viewport
    _columnWidth = _columnWidth.clamp(50.0, 100.0); // FIXED: Adjusted range for better spacing
    
    developer.log('FoundationHistoryChart: Layout calculated - availableWidth: $availableWidth, '
        'chartAreaWidth: $chartAreaWidth, columnWidth: $_columnWidth, '
        'visibleDataPoints: $_visibleDataPoints, totalDataPoints: ${_allDataPoints.length}');
  }

  /// NEW: Get max value for History data (count instead of percentage)
  double _getMaxValue() {
    if (_allDataPoints.isEmpty) return 5.0;
    final maxValue = _allDataPoints.map((p) => p.value).reduce((a, b) => a > b ? a : b);
    return (maxValue * 1.2).clamp(1.0, double.infinity); // Add 20% buffer
  }
}

/// COPIED FROM SCORE CHART: Y-axis painter with modifications for History data
class _YAxisPainter extends CustomPainter {
  final ThemeData theme;
  final double chartAreaHeight;
  final double baselineY;
  final double topPadding;
  final double maxValue;

  _YAxisPainter({
    required this.theme,
    required this.chartAreaHeight,
    required this.baselineY,
    required this.topPadding,
    required this.maxValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // MODIFIED: Show count values instead of percentages
    final intervals = _calculateIntervals(maxValue);
    
    for (final value in intervals) {
      final y = baselineY - (value / maxValue * chartAreaHeight);
      
      // Draw label
      final textPainter = TextPainter(
        text: TextSpan(
          text: value.toInt().toString(), // Show count, not percentage
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: value == 0 ? FontWeight.w600 : FontWeight.w500,
            color: value == 0 
                ? theme.colorScheme.onSurface
                : theme.colorScheme.onSurfaceVariant,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.right,
      );
      
      textPainter.layout();
      textPainter.paint(canvas, Offset(size.width - textPainter.width - 5, y - textPainter.height / 2));
      
      // Draw grid line
      final gridPaint = Paint()
        ..color = theme.colorScheme.outline.withOpacity(value == 0 ? 0.6 : 0.3)
        ..strokeWidth = value == 0 ? 2.0 : 1.0;
      
      canvas.drawLine(Offset(size.width - 5, y), Offset(size.width, y), gridPaint);
    }
  }

  /// Calculate appropriate intervals for count values
  List<double> _calculateIntervals(double maxValue) {
    if (maxValue <= 5) return [0, 1, 2, 3, 4, 5];
    if (maxValue <= 10) return [0, 2, 4, 6, 8, 10];
    if (maxValue <= 20) return [0, 5, 10, 15, 20];
    
    final step = (maxValue / 4).ceilToDouble();
    return List.generate(5, (i) => i * step);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// NEW: Bar painter for History Chart (replaces line painter from Score Chart)
class _HistoryBarPainter extends CustomPainter {
  final ThemeData theme;
  final List<ChartDataPoint> dataPoints;
  final double columnWidth;
  final double chartAreaHeight;
  final double baselineY;
  final double topPadding;
  final double maxValue;
  final TimeScale timeScale;
  final HabitAnalyticsService analyticsService; // Add analytics service

  _HistoryBarPainter({
    required this.theme,
    required this.dataPoints,
    required this.columnWidth,
    required this.chartAreaHeight,
    required this.baselineY,
    required this.topPadding,
    required this.maxValue,
    required this.timeScale,
    required this.analyticsService, // Add analytics service parameter
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) {
      developer.log('FoundationHistoryChart: No data points to paint');
      return;
    }

    developer.log('FoundationHistoryChart: Painting ${dataPoints.length} bars with columnWidth: $columnWidth, maxValue: $maxValue');

    // Draw bars
    for (int i = 0; i < dataPoints.length; i++) {
      final dataPoint = dataPoints[i];
      final x = i * columnWidth;
      final barHeight = (dataPoint.value / maxValue) * chartAreaHeight;
      final barTop = baselineY - barHeight;
      
      // FIXED: Ensure minimum bar height for visibility
      final minBarHeight = 2.0; // Minimum 2px height
      final actualBarHeight = barHeight < minBarHeight && dataPoint.value > 0 ? minBarHeight : barHeight;
      final actualBarTop = baselineY - actualBarHeight;
      
      // FIXED: Enhanced smart color coding for day view with debug logging
      Color barColor;
      final isFirstOfMonth = dataPoint.date.day == 1;
      final isStartOfWeek = timeScale == TimeScale.day ? _isStartOfWeek(dataPoint.date) : false;
      
      // DEBUG: Log color decisions for first few bars
      if (i < 5) {
        developer.log('FoundationHistoryChart: Bar $i color decision - Date: ${dataPoint.date}, '
            'isFirstOfMonth: $isFirstOfMonth, isStartOfWeek: $isStartOfWeek, isFuture: ${dataPoint.isFuture}');
      }
      
      if (dataPoint.isFuture) {
        // Future dates: muted color
        barColor = theme.colorScheme.surfaceVariant.withOpacity(0.6);
      } else if (timeScale == TimeScale.day) {
        // FIXED: Enhanced smart color coding for day view
        if (isFirstOfMonth) {
          // First day of month: tertiary color (distinctive)
          barColor = theme.colorScheme.tertiary;
          if (i < 5) developer.log('FoundationHistoryChart: Using TERTIARY color for first of month');
        } else if (isStartOfWeek) {
          // Start of week: secondary color
          barColor = theme.colorScheme.secondary;
          if (i < 5) developer.log('FoundationHistoryChart: Using SECONDARY color for week start');
        } else {
          // Regular days: primary color
          barColor = theme.colorScheme.primary;
          if (i < 5) developer.log('FoundationHistoryChart: Using PRIMARY color for regular day');
        }
      } else {
        // Other time scales: use original logic
        if (_isStartOfWeek(dataPoint.date)) {
          barColor = Colors.orange.shade600; // Week start highlighting
        } else {
          barColor = theme.colorScheme.primary;
        }
      }
      
      // FIXED: Ensure bars are always visible with proper dimensions
      final barLeft = x + columnWidth * 0.15; // Slightly wider bars
      final barWidth = columnWidth * 0.7; // 70% of column width
      
      // Draw bar
      final barRect = RRect.fromRectAndRadius(
        Rect.fromLTWH(
          barLeft,
          actualBarTop,
          barWidth,
          actualBarHeight,
        ),
        const Radius.circular(3), // Slightly smaller radius for better visibility
      );
      
      final barPaint = Paint()
        ..color = barColor
        ..style = PaintingStyle.fill;
      
      canvas.drawRRect(barRect, barPaint);
      
      // DEBUG: Log bar drawing details for first few bars
      if (i < 3) {
        developer.log('FoundationHistoryChart: Bar $i - value: ${dataPoint.value}, height: $actualBarHeight, '
            'rect: (${barLeft.toStringAsFixed(1)}, ${actualBarTop.toStringAsFixed(1)}, '
            '${barWidth.toStringAsFixed(1)}, ${actualBarHeight.toStringAsFixed(1)})');
      }
      
      // Draw value labels on top of bars (like Score Chart)
      if (dataPoint.value > 0) {
        _drawValueLabel(canvas, dataPoint.value, x + columnWidth / 2, actualBarTop - 8);
      }
    }
    
    // Draw X-axis labels
    _drawXAxisLabels(canvas, size);
  }

  /// Draw value labels on bars (copied from Score Chart pattern)
  void _drawValueLabel(Canvas canvas, double value, double x, double y) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: value.toInt().toString(), // Show count
        style: GoogleFonts.inter(
          fontSize: 7,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          shadows: [
            Shadow(
              offset: const Offset(0.5, 0.5),
              blurRadius: 1.5,
              color: theme.colorScheme.primary.withOpacity(0.7),
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    textPainter.layout();
    
    canvas.save();
    canvas.translate(x, y - 18);
    canvas.rotate(-0.5); // Same rotation as Score Chart
    textPainter.paint(canvas, Offset(-textPainter.width / 2, -textPainter.height / 2));
    canvas.restore();
  }
  
  /// Draw X-axis labels (copied from Score Chart)
  void _drawXAxisLabels(Canvas canvas, Size size) {
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = i * columnWidth + columnWidth / 2;
      
      final textPainter = TextPainter(
        text: TextSpan(
          text: point.label,
          style: GoogleFonts.inter(
            fontSize: 10,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, baselineY + 10));
    }
  }

  /// Check if date is start of week using user's preference
  bool _isStartOfWeek(DateTime date) {
    // Use the analytics service to determine start of week
    // This will respect the user's start-of-week preference (Sunday or Monday)
    return analyticsService.isStartOfWeekForDate(date);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // FIXED: Only repaint when data actually changes to prevent disappearing bars
    if (oldDelegate is! _HistoryBarPainter) return true;
    return oldDelegate.dataPoints != dataPoints ||
           oldDelegate.columnWidth != columnWidth ||
           oldDelegate.maxValue != maxValue ||
           oldDelegate.timeScale != timeScale;
  }
}