import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'theme_notifier.dart';
import 'modern_habits_screen.dart';
import 'settings_service.dart';
import 'modern_theme.dart';
import 'habit_details_screen.dart';
import 'habit.dart';
import 'database_service.dart';
import 'entry.dart';

void main() async {
  // Enhanced build debugging and error recovery
  try {
    debugPrint('[BUILD_DEBUG] === STARTING APPLICATION INITIALIZATION ===');
    debugPrint('[BUILD_DEBUG] Flutter Framework Version: ${_getFlutterVersion()}');
    debugPrint('[BUILD_DEBUG] Platform: ${_getPlatformInfo()}');
    
    // Ensure Flutter binding is initialized
    WidgetsFlutterBinding.ensureInitialized();
    debugPrint('[BUILD_DEBUG] Flutter binding initialized successfully');
    
    // Validate build environment
    await _validateBuildEnvironment();
    
    // Initialize settings service with comprehensive debugging
    debugPrint('[BUILD_DEBUG] === INITIALIZING SETTINGS SERVICE ===');
    final settingsService = SettingsService.instance;
    debugPrint('[BUILD_DEBUG] Settings service instance created');
    
    await settingsService.initialize();
    debugPrint('[BUILD_DEBUG] Settings service initialized successfully');
    
    // Initialize ThemeNotifier with persistence
    debugPrint('[BUILD_DEBUG] === INITIALIZING THEME NOTIFIER ===');
    final themeNotifier = ThemeNotifier();
    await themeNotifier.initialize();
    debugPrint('[BUILD_DEBUG] ThemeNotifier initialized with saved preferences');
    
    // Start application with enhanced monitoring
    debugPrint('[BUILD_DEBUG] === STARTING APPLICATION ===');
    runApp(
      ChangeNotifierProvider.value(
        value: themeNotifier,
        child: const MyApp(),
      ),
    );
    debugPrint('[BUILD_DEBUG] Application started successfully');
    debugPrint('[BUILD_DEBUG] === INITIALIZATION COMPLETE ===');
    
  } catch (error, stackTrace) {
    // Comprehensive error analysis and recovery
    debugPrint('[BUILD_ERROR] === CRITICAL INITIALIZATION FAILURE ===');
    debugPrint('[BUILD_ERROR] Error Type: ${error.runtimeType}');
    debugPrint('[BUILD_ERROR] Error Message: $error');
    debugPrint('[BUILD_ERROR] Stack Trace: $stackTrace');
    debugPrint('[BUILD_ERROR] Build Environment: ${_getBuildEnvironmentInfo()}');
    
    // Enhanced console output for debugging
    print('=== CRITICAL BUILD/STARTUP ERROR ===');
    print('Error Type: ${error.runtimeType}');
    print('Error: $error');
    print('=== STACK TRACE ===');
    print(stackTrace);
    print('=== BUILD ENVIRONMENT ===');
    print(_getBuildEnvironmentInfo());
    print('=====================================');
    
    // Attempt recovery with minimal initialization
    debugPrint('[BUILD_RECOVERY] Attempting minimal app startup...');
    try {
      runApp(
        ChangeNotifierProvider(
          create: (_) => ThemeNotifier(),
          child: const MyApp(),
        ),
      );
      debugPrint('[BUILD_RECOVERY] Minimal startup successful');
    } catch (recoveryError) {
      debugPrint('[BUILD_RECOVERY] FAILED: $recoveryError');
      print('FATAL: Unable to start application even in recovery mode');
      print('Recovery Error: $recoveryError');
    }
  }
}

// Build environment validation and debugging helpers
Future<void> _validateBuildEnvironment() async {
  try {
    debugPrint('[BUILD_VALIDATION] === VALIDATING BUILD ENVIRONMENT ===');
    
    // Check Flutter installation
    debugPrint('[BUILD_VALIDATION] Flutter installation: ${_getFlutterVersion()}');
    
    // Check platform-specific requirements
    debugPrint('[BUILD_VALIDATION] Platform: ${_getPlatformInfo()}');
    
    // Validate dependencies
    debugPrint('[BUILD_VALIDATION] Validating dependencies...');
    // Note: In a real app, you might check specific dependencies here
    
    debugPrint('[BUILD_VALIDATION] Build environment validation complete');
  } catch (e) {
    debugPrint('[BUILD_VALIDATION] WARNING: Validation failed - $e');
  }
}

String _getFlutterVersion() {
  try {
    debugPrint('[VERSION_DEBUG] === GETTING FLUTTER VERSION ===');
    debugPrint('[VERSION_DEBUG] Checking Flutter framework availability');
    
    // Check if we're in debug mode
    final isDebugMode = kDebugMode;
    debugPrint('[VERSION_DEBUG] Debug mode: $isDebugMode');
    
    // Check if we're on web
    final isWeb = kIsWeb;
    debugPrint('[VERSION_DEBUG] Web platform: $isWeb');
    
    return 'Flutter Framework Available (Debug: $isDebugMode, Web: $isWeb)';
  } catch (e, stackTrace) {
    debugPrint('[VERSION_DEBUG] ERROR: Failed to get Flutter version info');
    debugPrint('[VERSION_DEBUG] Error: $e');
    debugPrint('[VERSION_DEBUG] StackTrace: $stackTrace');
    return 'Flutter Version Unknown: $e';
  }
}

String _getPlatformInfo() {
  try {
    debugPrint('[PLATFORM_DEBUG] === GETTING PLATFORM INFO ===');
    debugPrint('[PLATFORM_DEBUG] Attempting to get defaultTargetPlatform');
    
    final platform = defaultTargetPlatform;
    debugPrint('[PLATFORM_DEBUG] Platform detected: ${platform.name}');
    
    return 'Platform: ${platform.name}';
  } catch (e, stackTrace) {
    debugPrint('[PLATFORM_DEBUG] ERROR: Failed to get platform info');
    debugPrint('[PLATFORM_DEBUG] Error: $e');
    debugPrint('[PLATFORM_DEBUG] StackTrace: $stackTrace');
    
    // Fallback platform detection
    try {
      debugPrint('[PLATFORM_DEBUG] Attempting fallback platform detection');
      if (kIsWeb) {
        debugPrint('[PLATFORM_DEBUG] Fallback: Detected web platform');
        return 'Platform: web';
      } else {
        debugPrint('[PLATFORM_DEBUG] Fallback: Detected mobile/desktop platform');
        return 'Platform: mobile/desktop';
      }
    } catch (fallbackError) {
      debugPrint('[PLATFORM_DEBUG] Fallback failed: $fallbackError');
      return 'Platform: Unknown ($e)';
    }
  }
}

String _getBuildEnvironmentInfo() {
  try {
    debugPrint('[BUILD_ENV_DEBUG] === GENERATING BUILD ENVIRONMENT INFO ===');
    
    final flutterInfo = _getFlutterVersion();
    debugPrint('[BUILD_ENV_DEBUG] Flutter info: $flutterInfo');
    
    final platformInfo = _getPlatformInfo();
    debugPrint('[BUILD_ENV_DEBUG] Platform info: $platformInfo');
    
    final buildMode = kDebugMode ? 'Debug' : (kProfileMode ? 'Profile' : 'Release');
    debugPrint('[BUILD_ENV_DEBUG] Build mode: $buildMode');
    
    final isWeb = kIsWeb;
    debugPrint('[BUILD_ENV_DEBUG] Is web: $isWeb');
    
    final envInfo = '''
Flutter Framework: $flutterInfo
Platform: $platformInfo
Dart VM: Available
Build Mode: $buildMode
Web Platform: $isWeb
Debug Mode: $kDebugMode
Profile Mode: $kProfileMode
Release Mode: $kReleaseMode
''';
    
    debugPrint('[BUILD_ENV_DEBUG] Complete environment info generated');
    return envInfo;
    
  } catch (e, stackTrace) {
    debugPrint('[BUILD_ENV_DEBUG] ERROR: Failed to generate environment info');
    debugPrint('[BUILD_ENV_DEBUG] Error: $e');
    debugPrint('[BUILD_ENV_DEBUG] StackTrace: $stackTrace');
    
    return '''
Flutter Framework: Error getting info ($e)
Platform: Error getting info
Dart VM: Available
Build Mode: Unknown
''';
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeNotifier>(
      builder: (context, themeNotifier, child) {
        return MaterialApp(
          title: 'Habits Tracker',
          theme: ModernTheme.lightTheme,
          darkTheme: ModernTheme.darkTheme,
          themeMode: themeNotifier.themeMode,
          home: const TestHabitDetailsScreen(), // TEMP: Direct navigation to test History Chart
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}

/// TEMP: Test wrapper to automatically show habit details screen with test data
class TestHabitDetailsScreen extends StatefulWidget {
  const TestHabitDetailsScreen({super.key});

  @override
  State<TestHabitDetailsScreen> createState() => _TestHabitDetailsScreenState();
}

class _TestHabitDetailsScreenState extends State<TestHabitDetailsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  Habit? _testHabit;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _createTestHabitAndNavigate();
  }

  Future<void> _createTestHabitAndNavigate() async {
    try {
      debugPrint('[TEST_NAVIGATION] === CREATING TEST HABIT FOR HISTORY CHART ===');
      
      // Load or create a test habit with entries
      final habits = await _databaseService.loadAllHabitsWithEntries();
      
      if (habits.isNotEmpty) {
        // Use existing habit
        _testHabit = habits.first;
        debugPrint('[TEST_NAVIGATION] Using existing habit: ${_testHabit!.name}');
      } else {
        // Create a test habit with sample data
        final testHabit = Habit(
          id: DateTime.now().millisecondsSinceEpoch.toString(), // Convert to String
          name: 'Test Habit for History Chart',
          sectionIds: [],
          type: HabitType.boolean,
          createdAt: DateTime.now(),
        );
        
        // Add some sample entries for the last 2 weeks
        final now = DateTime.now();
        for (int i = 0; i < 14; i++) {
          final entryDate = now.subtract(Duration(days: i));
          final entry = Entry(
            id: (DateTime.now().millisecondsSinceEpoch + i).toString(), // Convert to String
            habitId: testHabit.id,
            timestamp: entryDate,
            value: i % 3 != 0, // Complete 2 out of 3 days (boolean value for boolean type)
            type: EntryType.boolean,
          );
          testHabit.entries.add(entry);
        }
        
        await _databaseService.saveHabit(testHabit);
        _testHabit = testHabit;
        debugPrint('[TEST_NAVIGATION] Created test habit with ${testHabit.entries.length} entries');
      }
      
      setState(() => _isLoading = false);
      
    } catch (e) {
      debugPrint('[TEST_NAVIGATION] Error creating test habit: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _testHabit == null) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Setting up test habit for History Chart...'),
            ],
          ),
        ),
      );
    }

    // Navigate directly to habit details screen
    return HabitDetailsScreen(habit: _testHabit!);
  }
}